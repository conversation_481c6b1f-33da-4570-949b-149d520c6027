# encoding: utf-8
---
recipe:
  Rx: others/recipes/full
  args:
  description: >-
    完整安装、更新
install_files: >-
  cn_dicts/*.*
  en_dicts/*.*
  opencc/*.*
  rime.lua
  lua/*.*
  lua/cold_word_drop/*.*
  default.yaml
  squirrel.yaml
  weasel.yaml
  rime_ice.schema.yaml
  rime_ice.dict.yaml
  t9.schema.yaml
  double_pinyin.schema.yaml
  double_pinyin_abc.schema.yaml
  double_pinyin_mspy.schema.yaml
  double_pinyin_sogou.schema.yaml
  double_pinyin_flypy.schema.yaml
  double_pinyin_ziguang.schema.yaml
  symbols_v.yaml
  symbols_caps_v.yaml
  radical_pinyin.schema.yaml
  radical_pinyin.dict.yaml
  melt_eng.schema.yaml
  melt_eng.dict.yaml
  custom_phrase.txt
