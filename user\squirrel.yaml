# Squirrel settings
# encoding: utf-8
#
# squirrel[.custom].yaml 是鼠须管的前端配置文件，小狼毫是 weasel[.custom].yaml
# 各平台皮肤配置并不一致。
#
# 鼠须管内置皮肤展示： https://github.com/NavisLab/rime-pifu
# 鼠须管界面配置指南： https://github.com/LEOYoon-Tsaw/Rime_collections/blob/master/鼠鬚管介面配置指南.md
# 鼠须管作者写的图形化的皮肤设计器： https://github.com/LEOYoon-Tsaw/Squirrel-Designer


# 要比共享目录的同名文件的 config_version 大才可以生效
config_version: '2023-02-27'


# options: last | default | _custom_
# last: the last used latin keyboard layout
# default: US (ABC) keyboard layout
# _custom_: keyboard layout of your choice, e.g. 'com.apple.keylayout.USExtended' or simply 'USExtended'
keyboard_layout: default
# for veteran chord-typist
chord_duration: 0.1  # seconds
# options: always | never | appropriate
show_notifications_when: appropriate


# ascii_mode、inline、no_inline、vim_mode 等等设定
# 可参考 /Library/Input Methods/Squirrel.app/Contents/SharedSupport/squirrel.yaml
app_options:
  # com.apple.Spotlight:
  #   ascii_mode: true    # 开启默认英文
  # com.microsoft.VSCode:
  #   ascii_mode: false   # 关闭默认英文


style:
  # 选择皮肤，亮色与暗色主题
  color_scheme: purity_of_form_custom
  color_scheme_dark: purity_of_form_custom

  # 预设选项。如果皮肤没写，则使用这些属性；如果皮肤写了，使用皮肤的。
  text_orientation: horizontal  # horizontal | vertical
  inline_preedit: true
  corner_radius: 10
  hilited_corner_radius: 0
  border_height: 0
  border_width: 0
  line_spacing: 5
  spacing: 10
  #candidate_format: '%c. %@'
  #base_offset: 6
  font_face: 'Lucida Grande'
  font_point: 21
  #label_font_face: 'Lucida Grande'
  label_font_point: 18
  #comment_font_face: 'Lucida Grande'
  comment_font_point: 18


# 皮肤列表
preset_color_schemes:
  # 对 purity_of_form 略微调整颜色，让色彩更柔和点，补全其他选项和注释
  purity_of_form_custom:
    name: "純粹的形式／Purity of Form Custom"
    author: 雨過之後、佛振
    # 如果将字体设置为 PingFangSC-Regular
    # 会让 🈶🈚️🉑🈲🉐 这几个 Emoji 失去彩色效果，留空反而可以显示。。。
    font_face: ""                   # 字体及大小
    font_point: 18
    label_font_face: "Helvetica"    # 序号字体及大小
    label_font_point: 12
    comment_font_face: "Helvetica"  # 注字体及大小
    comment_font_point: 16
    # candidate_list_layout: stacked        # 候选项排列方向 stacked(默认) | linear
    # text_orientation: horizontal          # 文字排列方向 horizontal(默认) | vertical
    inline_preedit: true                    # 拼音位于： 候选框 false | 行内 true
    translucency: false                     # 磨砂： false | true
    mutual_exclusive: false                 # 色不叠加： false | true
    border_height: 0                        # 外边框 高
    border_width: 0                         # 外边框 宽
    corner_radius: 10                       # 外边框 圆角半径
    hilited_corner_radius: 0                # 选中框 圆角半径
    surrounding_extra_expansion: 0          # 候选项背景相对大小？
    shadow_size: 0                          # 阴影大小
    line_spacing: 5                         # 行间距
    base_offset: 0                          # 字基高
    alpha: 1                                # 透明度，0~1
    spacing: 10                             # 拼音与候选项之间的距离 （inline_preedit: false）
    color_space: srgb                       # 色彩空间： srgb | display_p3
    back_color: 0x545554                    # 底色
    hilited_candidate_back_color: 0xE3E3E3  # 选中底色
    label_color: 0xBBBBBB                   # 序号颜色
    hilited_candidate_label_color: 0x4C4C4C # 选中序号颜色
    candidate_text_color: 0xEEEEEE          # 文字颜色
    hilited_candidate_text_color: 0x000000  # 选中文字颜色
    comment_text_color: 0x808080            # 注颜色
    hilited_comment_text_color: 0x808080    # 选中注颜色
    text_color: 0x808080                    # 拼音颜色 （inline_preedit: false）
    hilited_text_color: 0xEEEEEE            # 选中拼音颜色 （inline_preedit: false）
    # candidate_back_color:                 # 候选项底色
    # preedit_back_color:                   # 拼音底色 （inline_preedit: false）
    # hilited_back_color:                   # 选中拼音底色 （inline_preedit: false）
    # border_color:                         # 外边框颜色


  # 下面是内置的皮肤 /Library/Input Methods/Squirrel.app/Contents/SharedSupport/squirrel.yaml


  native:
    name: 系統配色

  aqua:
    name: 碧水／Aqua
    author: 佛振 <<EMAIL>>
    text_color: 0x606060
    back_color: 0xeeeceeee
    candidate_text_color: 0x000000
    hilited_text_color: 0x000000
    hilited_candidate_text_color: 0xffffff
    hilited_candidate_back_color: 0xeefa3a0a
    comment_text_color: 0x5a5a5a
    hilited_comment_text_color: 0xfcac9d

  azure:
    name: 青天／Azure
    author: 佛振 <<EMAIL>>
    text_color: 0xcfa677
    candidate_text_color: 0xffeacc
    back_color: 0xee8b4e01
    hilited_text_color: 0xffeacc
    hilited_candidate_text_color: 0x7ffeff
    hilited_candidate_back_color: 0x00000000
    comment_text_color: 0xc69664

  luna:
    name: 明月／Luna
    author: 佛振 <<EMAIL>>
    text_color: 0xa5a5a5
    back_color: 0xdd000000
    candidate_text_color: 0xeceeee
    hilited_text_color: 0x7fffff
    hilited_candidate_text_color: 0x7fffff
    hilited_candidate_back_color: 0x40000000
    comment_text_color: 0xa5a5a5
    hilited_comment_text_color: 0x449c9d

  ink:
    name: 墨池／Ink
    author: 佛振 <<EMAIL>>
    text_color: 0x5a5a5a
    back_color: 0xeeffffff
    candidate_text_color: 0x000000
    hilited_text_color: 0x000000
    #hilited_back_color: 0xdddddd
    hilited_candidate_text_color: 0xffffff
    hilited_candidate_back_color: 0xcc000000
    comment_text_color: 0x5a5a5a
    hilited_comment_text_color: 0x808080

  lost_temple:
    name: 孤寺／Lost Temple
    author: 佛振 <<EMAIL>>, based on ir_black
    text_color: 0xe8f3f6
    back_color: 0xee303030
    hilited_text_color: 0x82e6ca
    hilited_candidate_text_color: 0x000000
    hilited_candidate_back_color: 0x82e6ca
    comment_text_color: 0xbb82e6ca
    hilited_comment_text_color: 0xbb203d34

  dark_temple:
    name: 暗堂／Dark Temple
    author: 佛振 <<EMAIL>>, based on ir_black
    text_color: 0x92f6da
    back_color: 0x222222
    candidate_text_color: 0xd8e3e6
    hilited_text_color: 0xffcf9a
    hilited_back_color: 0x222222
    hilited_candidate_text_color: 0x92f6da
    hilited_candidate_back_color: 0x10000000  # 0x333333
    comment_text_color: 0x606cff

  psionics:
    name: 幽能／Psionics
    author: 雨過之後、佛振
    text_color: 0xc2c2c2
    back_color: 0x444444
    candidate_text_color: 0xeeeeee
    hilited_text_color: 0xeeeeee
    hilited_back_color: 0x444444
    hilited_candidate_label_color: 0xfafafa
    hilited_candidate_text_color: 0xfafafa
    hilited_candidate_back_color: 0xd4bc00
    comment_text_color: 0x808080
    hilited_comment_text_color: 0x444444

  purity_of_form:
    name: 純粹的形式／Purity of Form
    author: 雨過之後、佛振
    text_color: 0xc2c2c2
    back_color: 0x444444
    candidate_text_color: 0xeeeeee
    hilited_text_color: 0xeeeeee
    hilited_back_color: 0x444444
    hilited_candidate_text_color: 0x000000
    hilited_candidate_back_color: 0xfafafa
    comment_text_color: 0x808080

  purity_of_essence:
    name: 純粹的本質／Purity of Essence
    author: 佛振
    text_color: 0x2c2ccc
    back_color: 0xfafafa
    candidate_text_color: 0x000000
    hilited_text_color: 0x000000
    hilited_back_color: 0xfafafa
    hilited_candidate_text_color: 0xeeeeee
    hilited_candidate_back_color: 0x444444
    comment_text_color: 0x808080

  starcraft:
    name: 星際我爭霸／StarCraft
    author: Contralisk <<EMAIL>>, original artwork by Blizzard Entertainment
    text_color: 0xccaa88
    candidate_text_color: 0x30bb55
    back_color: 0xee000000
    border_color: 0x1010a0
    hilited_text_color: 0xfecb96
    hilited_back_color: 0x000000
    hilited_candidate_text_color: 0x70ffaf
    hilited_candidate_back_color: 0x00000000
    comment_text_color: 0x1010d0
    hilited_comment_text_color: 0x1010f0

  google:
    name: 谷歌／Google
    author: skoj <<EMAIL>>
    text_color: 0x666666 #拼音串
    candidate_text_color: 0x000000 #非第一候选项
    back_color: 0xFFFFFF #背景
    border_color: 0xE2E2E2 #边框
    hilited_text_color: 0x000000 #拼音串高亮
    hilited_back_color: 0xFFFFFF #拼音串高亮背景
    hilited_candidate_text_color: 0xFFFFFF #第一候选项
    hilited_candidate_back_color: 0xCE7539 #第一候选项背景
    comment_text_color: 0x6D6D6D #注解文字
    hilited_comment_text_color: 0xEBC6B0 #注解文字高亮

  solarized_rock:
    name: 曬經石／Solarized Rock
    author: "Aben <<EMAIL>>, based on Ethan Schoonover's Solarized color scheme"
    back_color: 0x362b00
    border_color: 0x362b00
    text_color: 0x8236d3
    hilited_text_color: 0x98a12a
    candidate_text_color: 0x969483
    comment_text_color: 0xc098a12a
    hilited_candidate_text_color: 0xffffff
    hilited_candidate_back_color: 0x8236d3
    hilited_comment_text_color: 0x362b00

  clean_white:
    name: 简约白／Clean White
    author: Chongyu Zhu <<EMAIL>>, based on 搜狗「简约白」
    horizontal: true
    candidate_format: '%c %@'
    corner_radius: 6
    border_height: 6
    border_width: 6
    font_point: 16
    label_font_point: 12
    label_color: 0x888888
    text_color: 0x808080
    hilited_text_color: 0x000000
    candidate_text_color: 0x000000
    comment_text_color: 0x808080
    back_color: 0xeeeeee
    hilited_candidate_label_color: 0xa0c98915
    hilited_candidate_text_color: 0xc98915
    hilited_candidate_back_color: 0xeeeeee

  apathy:
    name: 冷漠／Apathy
    author: LIANG Hai
    horizontal: true  # 水平排列
    inline_preedit: true #单行显示，false双行显示
    candidate_format: "%c\u2005%@\u2005"  # 编号 %c 和候选词 %@ 前后的空间
    corner_radius: 5  #候选条圆角
    border_height: 0
    border_width: 0
    back_color: 0xFFFFFF  #候选条背景色
    font_face: "PingFangSC-Regular,HanaMinB"  #候选词字体
    font_point: 16  #候选字词大小
    text_color: 0x424242  #高亮选中词颜色
    label_font_face: "STHeitiSC-Light"   #候选词编号字体
    label_font_point: 12   #候选编号大小
    hilited_candidate_text_color: 0xEE6E00  #候选文字颜色
    hilited_candidate_back_color: 0xFFF0E4  #候选文字背景色
    comment_text_color: 0x999999  #拼音等提示文字颜色

  dust:
    name: 浮尘／Dust
    author: Superoutman <<EMAIL>>
    horizontal: true  # 水平排列
    inline_preedit: true #单行显示，false双行显示
    candidate_format: "%c\u2005%@\u2005"  # 用 1/6 em 空格 U+2005 来控制编号 %c 和候选词 %@ 前后的空间。
    corner_radius: 2  #候选条圆角
    border_height: 3                                   # 窗口边界高度，大于圆角半径才生效
    border_width: 8                                    # 窗口边界宽度，大于圆角半径才生效
    back_color: 0xeeffffff  #候选条背景色
    border_color: 0xE0B693                             # 边框色
    font_face: "HYQiHei-55S Book,HanaMinA Regular"     #候选词字体
    font_point: 14  #候选字词大小
    label_font_face: "SimHei"   #候选词编号字体
    label_font_point: 10   #候选编号大小
    label_color: 0xcbcbcb                              # 预选栏编号颜色
    candidate_text_color: 0x555555                     # 预选项文字颜色
    text_color: 0x424242                               # 拼音行文字颜色，24位色值，16进制，BGR顺序
    comment_text_color: 0x999999                       # 拼音等提示文字颜色
    hilited_text_color: 0x9e9e9e                       # 高亮拼音 (需要开启内嵌编码)
    hilited_candidate_text_color: 0x000000             # 第一候选项文字颜色
    hilited_candidate_back_color: 0xfff0e4             # 第一候选项背景背景色
    hilited_candidate_label_color: 0x555555            # 第一候选项编号颜色
    hilited_comment_text_color: 0x9e9e9e               # 注解文字高亮

  mojave_dark:
    name: 沙漠夜／Mojave Dark
    author: xiehuc <<EMAIL>>
    horizontal: true                        # 水平排列
    inline_preedit: true                    # 单行显示，false双行显示
    candidate_format: "%c\u2005%@"    # 用 1/6 em 空格 U+2005 来控制编号 %c 和候选词 %@ 前后的空间。
    corner_radius: 5                        # 候选条圆角
    hilited_corner_radius: 3                # 高亮圆角
    border_height: 6                        # 窗口边界高度，大于圆角半径才生效
    border_width: 6                         # 窗口边界宽度，大于圆角半径才生效
    font_face: "PingFangSC"                 # 候选词字体
    font_point: 16                          # 候选字词大小
    label_font_point: 14                    # 候选编号大小

    text_color: 0xdedddd                    # 拼音行文字颜色，24位色值，16进制，BGR顺序
    back_color: 0x252320                    # 候选条背景色
    label_color: 0x888785                   # 预选栏编号颜色
    border_color: 0x020202                  # 边框色
    candidate_text_color: 0xdedddd          # 预选项文字颜色
    hilited_text_color: 0xdedddd            # 高亮拼音 (需要开启内嵌编码)
    hilited_back_color: 0x252320            # 高亮拼音 (需要开启内嵌编码)
    hilited_candidate_text_color: 0xffffff  # 第一候选项文字颜色
    hilited_candidate_back_color: 0xcb5d00  # 第一候选项背景背景色
    hilited_candidate_label_color: 0xffffff # 第一候选项编号颜色
    comment_text_color: 0xdedddd            # 拼音等提示文字颜色
    #hilited_comment_text_color: 0xdedddd    # 注解文字高亮

  solarized_light:
    name: 曬經・日／Solarized Light
    author: 雪齋 <<EMAIL>>
    color_space: display_p3 # Only available on macOS 10.12+
    back_color: 0xf0E5F6FB #Lab 97, 0, 10
    border_color: 0xf0EDFFFF #Lab 100, 0, 10
    preedit_back_color: 0xf0D7E8ED #Lab 92, 0, 10
    candidate_text_color: 0x3942CB #Lab 50, 65, 45
    label_color: 0x2566C6 #Lab 55, 45, 65
    comment_text_color: 0x8144C2 #Lab 50, 65, -5
    text_color: 0x756E5D #Lab 45, -7, -7
    hilited_back_color: 0xf0C9DADF #Lab 87, 0, 10
    hilited_candidate_back_color: 0x403516 #Lab 20, -12, -12
    hilited_candidate_text_color: 0x989F52 #Lab 60, -35, -5
    hilited_candidate_label_color: 0xCC8947 #Lab 55, -10, -45
    hilited_comment_text_color: 0x289989 #Lab 60, -20, 65
    hilited_text_color: 0xBE706D #Lab 50, 15, -45

  solarized_dark:
    name: 曬經・月／Solarized Dark
    author: 雪齋 <<EMAIL>>
    color_space: display_p3 # Only available on macOS 10.12+
    back_color: 0xf0352A0A #Lab 15, -12, -12
    border_color: 0xf02A1F00 #Lab 10, -12, -12
    preedit_back_color: 0xf0403516 #Lab 20, -12, -12
    candidate_text_color: 0x989F52 #Lab 60, -35, -5
    label_color: 0xCC8947 #Lab 55, -10, -45
    comment_text_color: 0x289989 #Lab 60, -20, 65
    text_color: 0xA1A095 #Lab 65, -05, -02
    hilited_back_color: 0xf04C4022 #Lab 25, -12, -12
    hilited_candidate_back_color: 0xD7E8ED #Lab 92, 0, 10
    hilited_candidate_text_color: 0x3942CB #Lab 50, 65, 45
    hilited_candidate_label_color: 0x2566C6 #Lab 55, 45, 65
    hilited_comment_text_color: 0x8144C2 #Lab 50, 65, -5
    hilited_text_color: 0x2C8BAE #Lab 60, 10, 65
