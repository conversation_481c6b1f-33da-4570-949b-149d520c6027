# Weasel settings
# encoding: utf-8

# 感谢 @[Mirtle](https://github.com/mirtlecn) 整理
# Rime 定制指南 <https://github.com/rime/home/<USER>/CustomizationGuide#定製指南>
# Weasel 定制文档 <https://github.com/rime/weasel/wiki/Weasel-定制化>
# Weasel 字体设定 <https://github.com/rime/weasel/wiki/字體設定>

# 部分选项需要将 Weasel 更新 16.0 及以上版本才能生效
config_version: "2024-05-14"

# [app_options]
# 针对特定应用的设置
app_options:
  firefox.exe:
    inline_preedit: true   # 行内显示预编辑区：规避 <https://github.com/rime/weasel/issues/946>
  # cmd.exe:               # 带 .exe 的进程名：Weasel 15.0 及之前版本须小写; PR #1049 合并后释出的版本大小写不敏感
  #   ascii_mode: true     # 英文模式
  # conhost.exe:
  #   ascii_mode: true
  # windowsterminal.exe:
  #   ascii_mode: true
  # wt.exe:
  #   ascii_mode: true
  # pwsh.exe:
  #   ascii_mode: true
  # powershell.exe:
  #   ascii_mode: true
  # mintty.exe:
  #   ascii_mode: true
  # nvim-qt.exe:
  #   ascii_mode: true
  #   vim_mode: true       # vim 模式, Esc <C-c> <C-[> 切换到 ascii 状态
# [End of <app_options>]

# [global settings]
show_notifications: true                   # 是否显示状态变化的通知：true；false；option_list（方案内的开头 option）
show_notifications_time: 1200              # 通知显示的时间，单位 ms
global_ascii: false                        # 切换为 ascii 模式时，是否影响所有窗口：true；false
# [End of <global settings>]

# [style]
# 字体；候选项、候选窗口的行为、布局及样式
style:
  color_scheme: purity_of_form_custom      # 默认配色方案
  # color_scheme_dark: nord                # 深色模式下，Weasel 的配色方案，Windows 10 1809+ 可用

  # 全局字体
  # 格式：字体1:起始码位:结束码位:字重:字形,字体2……，字体会依次 fallback
  # 详细设定请参考 <https://github.com/rime/weasel/wiki/字體設定>
  font_face: "Segoe UI Emoji, Microsoft YaHei, SF Pro, Noto Color Emoji"
  label_font_face: "Microsoft YaHei"       # 标签字体
  comment_font_face: "Microsoft YaHei"     # 注释字体
  font_point: 14                           # 全局字体字号
  label_font_point: 14                     # 标签字体字号，不设定 fallback 到 font_point
  comment_font_point: 13                   # 注释字体字号，不设定 fallback 到 font_point

  inline_preedit: true                     # 行内显示预编辑区：true；false
  preedit_type: composition                # 预编辑区内容：composition（编码）； preview（选中的候选）；preview_all（全部候选）

  fullscreen: false                        # 候选窗口全屏显示：true；false
  horizontal: true                         # 候选项横排：true；false
  vertical_text: false                     # 竖排文本：true；false
  # text_orientation: horizontal           # 文本排列方向，效果和 `vertical_text` 相同：horizontal；vertical
  vertical_text_left_to_right: false       # 竖排方向是否从左到右：true；false
  vertical_text_with_wrap: false           # 文本竖排模式下，自动换行：true；false
  vertical_auto_reverse: false             # 文本竖排模式下，候选窗口位于光标上方时倒序排列：true；false

  label_format: "%s"                       # 标签字符：例如 %s. 效果为 1. 2. 3. ....
  mark_text: ""                            # 标记字符，显示在选中的候选标签前，需要在配色方案中指定颜色；如该项为空字符串 "" 而配色方案中 hilited_mark_color 非透明色，则显示 Windows 11 输入法风格标记
  ascii_tip_follow_cursor: false           # 切换 ASCII 模式时，提示跟随鼠标，而非输入光标
  enhanced_position: true                  # 无法定位候选框时，在窗口左上角显示候选框：true；false
  display_tray_icon: false                 # 托盘显示独立于语言栏的额外图标：true；false
  antialias_mode: default                  # 次像素反锯齿设定：default；force_dword；cleartype；grayscale；aliased
  candidate_abbreviate_length: 30          # 候选项略写，超过此数字则用省略号代替。设置为 0 则不启用此功能
  mouse_hover_ms: 0                        # ! 已弃用。鼠标悬停选词响应时间（ms），设置为 0 时禁用该功能
  hover_type: none                         # 鼠标在候选窗口悬停时：none（无动作）；hilite（选中鼠标下的候选）；semi_hilite（高亮鼠标下的候选）

  paging_on_scroll: true                   # 在候选窗口上滑动滚轮的行为：true（翻页）；false （选中下一个候选）
  click_to_capture: false                  # 鼠标点击候选项，创建截图：true；false

  layout:
    baseline: 0                            # 字号百分比，与 linespacing 一同设置可解决字体跳动问题，设置为 0 为禁用
    linespacing: 0                         # 字号百分比，参考 <https://github.com/rime/weasel/pull/1177>
    align_type: center                     # 标签、候选文字、注解文字之间的相对对齐方式：top ; center ; bottom
    max_height: 600                        # 候选框最大高度，文本竖排模式下如高度超此尺寸则换列显示候选，设置为 0 不启用此功能
    max_width: 0                           # 候选框最大宽度，horizontal 布局如宽超此尺寸则换行显示候选，设置为 0 不启用此功能
    min_height: 0                          # 候选框最小高度
    min_width: 10                          # 候选框最小宽度
    border_width: 3                        # 边框宽度；又名 border
    margin_x: 12                           # 主体元素和候选框的左右边距；为负值时，不显示候选框
    margin_y: 12                           # 主体元素的上下边距；为负值时，不显示候选框
    spacing: 13                            # inline_preedit 为否时，编码区域和候选区域的间距
    candidate_spacing: 22                  # 候选项之间的间距
    hilite_spacing: 6                      # 候选项和相应标签的间距
    hilite_padding: 4                      # 高亮区域和内部文字的间距，影响高亮区域大小
    # hilite_padding_x: 8                    # 高亮区域和内部文字的左右间距，如无特殊指定则依 hilite_padding 设置
    # hilite_padding_y: 8                    # 高亮区域和内部文字的上下间距，如无特殊指定则依 hilite_padding 设置
    shadow_radius: 0                       # 阴影区域半径，为 0 不显示阴影；需要同时在配色方案中指定非透明的阴影颜色
    shadow_offset_x: 6                     # 阴影左右偏移距离
    shadow_offset_y: 4                     # 阴影上下偏移距离
    corner_radius: 8                       # 候选窗口圆角半径
    round_corner: 8                        # 候选背景色块圆角半径，又名 hilited_corner_radius
    # type: vertical                       # 布局设置，效果和 style 下的设置相同：
                                           # horizontal（横向）；vertical（竖向） ; vertical_text（竖排文本） ; vertical+fullscreen（全屏） ; horizontal+fullscreen（横向全屏）
# [End of <style>]

# [preset_color_schemes]
# 配色设定
# 在小狼毫用户目录新建 preview 文件夹，将自定义皮肤的截图重命名为 color_scheme_<name>.png 放入此文件夹，可以在「输入法设定」中看到自定义皮肤效果

# 小狼毫配色在线设计：
# [RIME 西米](https://fxliang.github.io/RimeSeeMe/)
# [小狼毫配色详解](https://github.com/rime/weasel/wiki/定制小狼毫配色)

preset_color_schemes:
  # 以下是一个配色方案示例
  nord:                                      # 在 `style/color_schema` 指定的配色方案值
    name: "远山／Nord"                       # 方案设置中显示的配色名称
    author: Mirtle                           # 配色作者名称
    color_format: rgba                       # 颜色格式：argb（0xaarrggbb）；rgba（0xrrggbbaa）；abgr（0xaabbggrr 默认）
    # 默认配色
    text_color: 0x2E3440                     # 文字
    shadow_color: 0x000000b4                 # 阴影
    label_color: 0x4C566A                    # 标签
    comment_text_color: 0xD08770             # 注释
    border_color: 0xECEFF4                   # 边框
    back_color: 0xECEFF4                     # 背景
    # 普通候选项配色
    candidate_back_color: 0xECEFF4           # 背景
    # candidate_border_color:                # 边框
    # candidate_shadow_color:                # 阴影
    candidate_text_color: 0x2E3440           # 文字
    # 编码区域配色
    hilited_text_color: 0x000000             # 文字
    hilited_back_color: 0xEBCB8B             # 背景
    # hilited_shadow_color:                  # 阴影
    # 选中的候选区域配色
    hilited_mark_color: 0xBF616A             # 标签前的标记
    hilited_label_color: 0x4C566A            # 标签
    hilited_comment_text_color: 0xBF616A     # 注释
    hilited_candidate_text_color: 0x2E3440   # 文字
    hilited_candidate_border_color: 0x8FBCBB # 边框
    hilited_candidate_back_color: 0x8FBCBB   # 背景
    # hilited_candidate_shadow_color:        # 阴影
    # inline_preedit: false 时翻页箭头颜色，不设置则不显示箭头
    # nextpage_color: 0x000000               # 下一页
    # prevpage_color: 0x000000               # 上一页

  # 修改自鼠须管配色方案
  purity_of_form_custom:
    name: "純粹的形式／Purity of Form Custom"
    author: 雨過之後、佛振
    text_color: 0x808080
    back_color: 0x545554
    label_color: 0xBBBBBB
    border_color: 0x545554
    shadow_color: 0xb4000000
    comment_text_color: 0x808080
    candidate_text_color: 0xEEEEEE
    hilited_text_color: 0xEEEEEE
    hilited_comment_text_color: 0x808080
    hilited_candidate_back_color: 0xE3E3E3
    hilited_candidate_border_color: 0xE3E3E3
    hilited_candidate_label_color: 0x4C4C4C
    hilited_candidate_text_color: 0x000000

  # 以下为 weasel 自带的配色方案
  # source: <https://github.com/rime/weasel/blob/master/output/data/weasel.yaml>

  aqua:
    name: 碧水／Aqua
    author: 佛振 <<EMAIL>>
    text_color: 0x000000
    back_color: 0xeceeee
    shadow_color: 0x00000000
    border_color: 0xe0e0e0
    hilited_text_color: 0x000000
    hilited_back_color: 0xd4d4d4
    hilited_shadow_color: 0x00000000
    hilited_candidate_text_color: 0xffffff
    hilited_candidate_back_color: 0xfa3a0a
    hilited_candidate_shadow_color: 0x00000000
    candidate_text_color: 0x000000
    candidate_back_color: 0xeceeee
    candidate_shadow_color: 0x00000000

  azure:
    name: 青天／Azure
    author: 佛振 <<EMAIL>>
    text_color: 0xffe8ca
    candidate_text_color: 0xfff8ee
    back_color: 0x8b4e01
    border_color: 0x8b4e01
    hilited_text_color: 0xfff8ee
    hilited_back_color: 0x8b4e01
    hilited_candidate_text_color: 0x7ffeff
    hilited_candidate_back_color: 0xa95e01
    comment_text_color: 0xc69664

  luna:
    name: 明月／Luna
    author: 佛振 <<EMAIL>>
    text_color: 0x000000
    back_color: 0xffffff
    border_color: 0xcccccc
    hilited_text_color: 0x000000
    hilited_back_color: 0x7fffff
    hilited_candidate_text_color: 0xffffff
    hilited_candidate_back_color: 0x000000

  ink:
    name: 墨池／Ink
    author: 佛振 <<EMAIL>>
    text_color: 0x000000
    back_color: 0xffffff
    border_color: 0x000000
    hilited_text_color: 0x000000
    hilited_back_color: 0xdddddd
    hilited_candidate_text_color: 0xffffff
    hilited_candidate_back_color: 0x000000

  lost_temple:
    name: 孤寺／Lost Temple
    author: 佛振 <<EMAIL>>, based on ir_black
    text_color: 0xe8f3f6
    back_color: 0x444444
    border_color: 0x444444
    hilited_text_color: 0x82e6ca
    hilited_back_color: 0x222222
    hilited_candidate_text_color: 0x000000
    hilited_candidate_back_color: 0x82e6ca

  dark_temple:
    name: 暗堂／Dark Temple
    author: 佛振 <<EMAIL>>, based on ir_black
    text_color: 0x92f6da
    candidate_text_color: 0xd8e3e6
    back_color: 0x222222
    border_color: 0x222222
    hilited_text_color: 0xffcf9a
    hilited_back_color: 0x222222
    hilited_candidate_text_color: 0x92f6da
    hilited_candidate_back_color: 0x333333
    comment_text_color: 0x606cff

  starcraft:
    name: 星際我爭霸／StarCraft
    author: Contralisk <<EMAIL>>, original artwork by Blizzard Entertainment
    text_color: 0xccaa88
    candidate_text_color: 0x30bb55
    back_color: 0x000000
    border_color: 0x1010a0
    hilited_text_color: 0xfecb96
    hilited_back_color: 0x000000
    hilited_candidate_text_color: 0x60ffa8
    hilited_candidate_back_color: 0x000000

  google:
    name: 谷歌／Google
    author: skoj <<EMAIL>>
    text_color: 0x666666
    candidate_text_color: 0x000000
    back_color: 0xFFFFFF
    border_color: 0xE2E2E2
    hilited_text_color: 0x000000
    hilited_back_color: 0xFFFFFF
    hilited_candidate_text_color: 0xFFFFFF
    hilited_candidate_back_color: 0xCE7539

  solarized_rock:
    name: 曬經石／Solarized Rock
    author: "Aben <<EMAIL>>, based on Ethan Schoonover's Solarized color scheme"
    back_color: 0x362b00
    border_color: 0x362b00
    text_color: 0x009985
    hilited_text_color: 0x98a12a
    candidate_text_color: 0x969483
    hilited_candidate_text_color: 0xffffff
    hilited_candidate_back_color: 0x8236d3

  tintin:
    name: "丁丁／ Tintin"
    author: "Patricivs <<EMAIL>>"
    text_color: 0xffffff
    back_color: 0xd99500
    border_color: 0xd99500
    label_color: 0xffffff
    candidate_text_color: 0xffffff
    comment_text_color: 0xffffff
    hilited_text_color: 0xf7d891
    hilited_back_color: 0xd99500
    hilited_candidate_text_color: 0xffffff
    hilited_comment_text_color: 0xffffff
    hilited_candidate_back_color: 0x2164f1

  dota_2:
    name: "DOTA 2"
    author: "Patricivs <<EMAIL>>"
    text_color: 0xffffff
    back_color: 0x120f10
    border_color: 0x120f10
    label_color: 0x5c758f
    hilited_text_color: 0x1841dd
    hilited_back_color: 0x120f10
    candidate_text_color: 0x5c758f
    comment_text_color: 0x5c758f
    hilited_candidate_text_color: 0xffffff
    hilited_comment_text_color: 0xffffff
    hilited_candidate_back_color: 0x1841dd

  brasil:
    name: "笆悉／Brasil"
    author: "Patricivs <<EMAIL>>"
    text_color: 0xffffff
    back_color: 0x559311
    border_color: 0x559311
    label_color: 0xffffff
    hilited_text_color: 0xffffff
    hilited_back_color: 0x7d3617
    candidate_text_color: 0xffffff
    comment_text_color: 0xffffff
    hilited_candidate_text_color: 0xffffff
    hilited_comment_text_color: 0xffffff
    hilited_candidate_back_color: 0x16c7f7

  doraemon:
    name: "銅鑼衛門／Doraemon"
    author: "Patricivs <<EMAIL>>"
    text_color: 0x1200e5
    back_color: 0xffffff
    border_color: 0xe89f00
    label_color: 0xe89f00
    hilited_text_color: 0xffffff
    hilited_back_color: 0x1200e5
    candidate_text_color: 0xe89f00
    comment_text_color: 0xe89f00
    hilited_candidate_text_color: 0xffffff
    hilited_comment_text_color: 0xffffff
    hilited_candidate_back_color: 0xe89f00

  espagna:
    name: "埃斯巴尼亞／España"
    author: "Patricivs <<EMAIL>>"
    text_color: 0xffffff
    back_color: 0x230dc3
    border_color: 0x230dc3
    label_color: 0xffffff
    hilited_text_color: 0xffffff
    hilited_back_color: 0x2cb5f7
    candidate_text_color: 0xffffff
    comment_text_color: 0xffffff
    hilited_candidate_text_color: 0xffffff
    hilited_comment_text_color: 0xffffff
    hilited_candidate_back_color: 0x2cb5f7

  gholabok:
    name: "胡蘿菔／Gholabok"
    author: "Patricivs <<EMAIL>>"
    text_color: 0xffffff
    back_color: 0x2939e8
    border_color: 0x2939e8
    label_color: 0xffffff
    hilited_text_color: 0xffffff
    hilited_back_color: 0x437b00
    candidate_text_color: 0xffffff
    comment_text_color: 0xffffff
    hilited_candidate_text_color: 0xffffff
    hilited_comment_text_color: 0xffffff
    hilited_candidate_back_color: 0x3d6ded

  kuma_shuzboz:
    name: "熊出沒／Kuma Shuzboz"
    author: "Patricivs <<EMAIL>>"
    text_color: 0x000000
    back_color: 0x2db6f8
    border_color: 0x2db6f8
    label_color: 0xffffff
    hilited_text_color: 0x2db6f8
    hilited_back_color: 0xffffff
    candidate_text_color: 0xffffff
    comment_text_color: 0xffffff
    hilited_candidate_text_color: 0xffffff
    hilited_comment_text_color: 0xffffff
    hilited_candidate_back_color: 0x000000

  kuon:
    name: "琨／Kuon"
    author: "Patricivs <<EMAIL>>"
    text_color: 0xffffff
    back_color: 0x70b33e
    border_color: 0x70b33e
    label_color: 0xffffff
    hilited_text_color: 0x70b33e
    hilited_back_color: 0xffffff
    candidate_text_color: 0xffffff
    comment_text_color: 0xffffff
    hilited_candidate_text_color: 0x70b33e
    hilited_comment_text_color: 0x70b33e
    hilited_candidate_back_color: 0xffffff

  macau:
    name: "澳門／Macau"
    author: "Patricivs <<EMAIL>>"
    text_color: 0x00d9ff
    back_color: 0x81a300
    border_color: 0x81a300
    label_color: 0xffffff
    hilited_text_color: 0xffffff
    hilited_back_color: 0x00d9ff
    candidate_text_color: 0xffffff
    comment_text_color: 0xffffff
    hilited_candidate_text_color: 0x00d9ff
    hilited_comment_text_color: 0x00d9ff
    hilited_candidate_back_color: 0xffffff

  nba:
    name: "NBA"
    author: "Patricivs <<EMAIL>>"
    text_color: 0xffffff
    back_color: 0xb76a00
    border_color: 0xb76a00
    label_color: 0xffffff
    hilited_text_color: 0x541ed7
    hilited_back_color: 0xffffff
    candidate_text_color: 0xffffff
    comment_text_color: 0xffffff
    hilited_candidate_text_color: 0xffffff
    hilited_comment_text_color: 0xffffff
    hilited_candidate_back_color: 0x541ed7

  ps4:
    name: "遊驛四／PS4"
    author: "Patricivs <<EMAIL>>"
    text_color: 0xffffff
    back_color: 0x000000
    border_color: 0x000000
    label_color: 0xffffff
    hilited_text_color: 0xffffff
    hilited_back_color: 0x575759
    candidate_text_color: 0xffffff
    comment_text_color: 0xffffff
    hilited_candidate_text_color: 0xffffff
    hilited_comment_text_color: 0xffffff
    hilited_candidate_back_color: 0xe89f00

  skype:
    name: "斯蓋普／Skype"
    author: "Patricivs <<EMAIL>>"
    text_color: 0xffffff
    back_color: 0xefad00
    border_color: 0xefad00
    label_color: 0xffffff
    hilited_text_color: 0xefad00
    hilited_back_color: 0xffffff
    candidate_text_color: 0xffffff
    comment_text_color: 0xffffff
    hilited_candidate_text_color: 0xefad00
    hilited_comment_text_color: 0xefad00
    hilited_candidate_back_color: 0xffffff

  xbox_silver:
    name: "銀色叉盒／Xbox Silver"
    author: "Patricivs <<EMAIL>>"
    text_color: 0x1fc28d
    back_color: 0xefeeee
    border_color: 0xefeeee
    label_color: 0x5bf0b5
    hilited_text_color: 0xffffff
    hilited_back_color: 0x5bf0b5
    candidate_text_color: 0x1fc28d
    comment_text_color: 0x1fc28d
    hilited_candidate_text_color: 0xffffff
    hilited_comment_text_color: 0xffffff
    hilited_candidate_back_color: 0x448c28

  youtube:
    name: "YouTube"
    author: "Patricivs <<EMAIL>>"
    text_color: 0x000000
    back_color: 0xdedede
    border_color: 0xdedede
    label_color: 0x000000
    hilited_text_color: 0x230dc3
    hilited_back_color: 0xffffff
    candidate_text_color: 0x000000
    comment_text_color: 0x000000
    hilited_candidate_text_color: 0xffffff
    hilited_comment_text_color: 0xffffff
    hilited_candidate_back_color: 0x230dc3

  so_young:
    name: "致青春／So Young"
    author: "五磅兔 <<EMAIL>>"
    text_color: 0x8236d3
    back_color: 0xe3f6fd
    border_color: 0xd5e8ee
    label_color: 0xa1a193
    candidate_text_color: 0x837b65
    comment_text_color: 0xd28b26
    hilited_text_color: 0x969483
    hilited_back_color: 0xd5e8ee
    hilited_candidate_text_color: 0xd5e8ee
    hilited_comment_text_color: 0xd5e8ee
    hilited_candidate_back_color: 0x98a12a

  smurfs:
    name: "藍精靈／Smurfs"
    author: "skoj <<EMAIL>>"
    text_color: 0xffffff
    back_color: 0xbf7817
    border_color: 0xf5ede0
    label_color: 0xbf7817
    hilited_text_color: 0xdbbc6d
    hilited_back_color: 0xbf7817
    candidate_text_color: 0xf6f6f6
    comment_text_color: 0xf6f6f6
    hilited_candidate_text_color: 0xf6f6f6
    hilited_comment_text_color: 0xf6f6f6
    hilited_candidate_back_color: 0xdbbc6d

  wii:
    name: "Wii"
    author: "Patricivs <<EMAIL>>"
    text_color: 0x575759
    back_color: 0xefefef
    border_color: 0xefefef
    label_color: 0xcac9c8
    hilited_text_color: 0xffcc33
    hilited_back_color: 0xefefef
    candidate_text_color: 0x575759
    comment_text_color: 0xcac9c8
    hilited_candidate_text_color: 0xffffff
    hilited_comment_text_color: 0xffffff
    hilited_candidate_back_color: 0xffcc33

  android:
    name: "安卓／Android"
    author: "Patricivs <<EMAIL>>"
    text_color: 0xffffff
    back_color: 0x99731c
    border_color: 0x99731c
    label_color: 0xc18835
    hilited_text_color: 0x50c4a8
    hilited_back_color: 0x99731c
    candidate_text_color: 0xffffff
    comment_text_color: 0xffffff
    hilited_candidate_text_color: 0xffffff
    hilited_comment_text_color: 0xffffff
    hilited_candidate_back_color: 0x50c4a8

  cool_breeze:
    name: "清風／Cool Breeze"
    author: "skoj <<EMAIL>>"
    text_color: 0x0000FF
    back_color: 0xFFFBFB
    border_color: 0xFFAAAA
    hilited_text_color: 0x0000CE
    hilited_back_color: 0xFFFBFB
    candidate_text_color: 0x009100
    hilited_candidate_text_color: 0x6F003A
    hilited_candidate_back_color: 0xFFD6AC

  google_plus:
    name: "Google+"
    author: "Patricivs <<EMAIL>>"
    text_color: 0xcac9c8
    back_color: 0xffffff
    border_color: 0x394bdd
    label_color: 0xcac9c8
    hilited_text_color: 0x394bdd
    hilited_back_color: 0xffffff
    candidate_text_color: 0x394bdd
    comment_text_color: 0xcac9c8
    hilited_candidate_text_color: 0xffffff
    hilited_comment_text_color: 0xffffff
    hilited_candidate_back_color: 0x394bdd

  modern_warfare:
    name: "現代戰爭／Modern Warfare"
    author: P1461
    text_color: 0x14bc70
    back_color: 0x0a1b0d
    border_color: 0x4bad83
    hilited_text_color: 0xfbfdfc
    hilited_back_color: 0x030e06
    candidate_text_color: 0xabfedc
    comment_text_color: 0xfcfdfb
    hilited_candidate_text_color: 0xabfedc
    hilited_candidate_back_color: 0x676f63

  brisk:
    name: "輕盈／Brisk"
    author: "skoj <<EMAIL>>"
    text_color: 0x2238dc
    back_color: 0xffffff
    border_color: 0x333333
    hilited_text_color: 0x2238dc
    hilited_back_color: 0xffffff
    candidate_text_color: 0x575757
    hilited_candidate_text_color: 0x2238dc
    hilited_candidate_back_color: 0xffffff

  starcraft_ii:
    name: "星際爭霸Ⅱ／StarCraft Ⅱ"
    author: "Patricivs <<EMAIL>>"
    text_color: 0xffffff
    back_color: 0x29190a
    border_color: 0x534b46
    label_color: 0xffffff
    hilited_text_color: 0xffffff
    hilited_back_color: 0x17100a
    candidate_text_color: 0xffffff
    comment_text_color: 0xffffff
    hilited_candidate_text_color: 0xffffff
    hilited_comment_text_color: 0xffffff
    hilited_candidate_back_color: 0xefad1e

  steam:
    name: "Steam"
    author: "Patricivs <<EMAIL>>"
    text_color: 0xcd8c52
    back_color: 0x141617
    border_color: 0x353638
    label_color: 0xffffff
    hilited_text_color: 0xc9cfd1
    hilited_back_color: 0x141617
    candidate_text_color: 0xffffff
    comment_text_color: 0xa7a7a9
    hilited_candidate_text_color: 0xffffff
    hilited_comment_text_color: 0xa7a7a9
    hilited_candidate_back_color: 0x594231

  flypy:
    # description: |
    #   小鹤飞扬：白底蓝字，红色高亮。
    #   根据小鹤双拼官网图片制作
    #   http://www.flypy.com/images/mr.png
    name: "小鹤飞扬／flypy"
    author: "Pal.lxk <<EMAIL>>"
    text_color: 0x000000
    back_color: 0xffffff
    border_color: 0xc6c6c6
    label_color: 0xff8000
    hilited_text_color: 0xff8000
    hilited_back_color: 0xffffff
    candidate_text_color: 0xff8000
    comment_text_color: 0xff8000
    hilited_candidate_text_color: 0x0000b0
    hilited_comment_text_color: 0x0000b0
    hilited_candidate_back_color: 0xffffff

  metroblue:
    name: "现代蓝／Metro Blue"
    author: "Prcuvu <<EMAIL>>"
    text_color: 0x000000
    back_color: 0xffffff
    border_color: 0xd77800
    label_color: 0x838383
    hilited_text_color: 0x000000
    hilited_back_color: 0xffffff
    candidate_text_color: 0x000000
    comment_text_color: 0x000000
    hilited_candidate_text_color: 0xffffff
    hilited_comment_text_color: 0xffffff
    hilited_candidate_back_color: 0xd77800
    hilited_label_color: 0xffffff

  psionics:
    name: 幽能／Psionics
    author: 雨過之後、佛振
    text_color: 0xc2c2c2
    back_color: 0x444444
    border_color: 0x444444
    candidate_text_color: 0xeeeeee
    hilited_text_color: 0xeeeeee
    hilited_back_color: 0x444444
    hilited_candidate_label_color: 0xfafafa
    hilited_candidate_text_color: 0xfafafa
    hilited_candidate_back_color: 0xd8bf00
    comment_text_color: 0x808080
    hilited_comment_text_color: 0x444444
# [End of <preset_color_schemes>]
