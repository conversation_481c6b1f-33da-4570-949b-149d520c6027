﻿# Rime schema
# vim: set sw=2 sts=2 et:
# encoding: utf-8
#
# 复制自 https://github.com/tumuyan/rime-melt，修改了拼写派生
# 感谢 @[Mirtle](https://github.com/mirtlecn) 贡献的极其好用的拼写派生
# 全拼和各个双拼有部分拼写规则不通用，需要修改 speller/algebra 下的 __include: algebra_rime_ice 为你所使用的方案

schema:
  schema_id: melt_eng
  name: Easy English Nano
  version: "2023-10-17"
  author:
    - <PERSON> <<EMAIL>>
    - tumuyan <<EMAIL>>
  description: Easy English Nano，只包含少量常用词汇，方便中英文混合输入度方案调用。

switches:
  - name: ascii_mode
    reset: 0
    states: [ASCII-OFF, ASCII-ON]

engine:
  processors:
    - ascii_composer
    - key_binder
    - speller
    - recognizer
    #關閉標點符號轉換（對應symbols.yaml）    - punctuator
    - selector
    - navigator
    - express_editor
  segmentors:
    - matcher
    - ascii_segmentor
    - abc_segmentor
    - punct_segmentor
    - fallback_segmentor
  translators:
    - table_translator
    - punct_translator
  filters:
    - uniquifier

speller:
  alphabet: zyxwvutsrqponmlkjihgfedcbaZYXWVUTSRQPONMLKJIHGFEDCBA
  delimiter: " '"
  algebra:
    # 根据当前所用拼音方案选择 __include 的值
    # 如果要在补丁中（melt_eng.custom.yaml）修改 algebra 以适配双拼，请参考 [常见问题](https://github.com/iDvel/rime-ice/issues/133)
    __include: algebra_rime_ice         # 全拼
    # __include: algebra_double_pinyin  # 自然码双拼
    # __include: algebra_flypy          # 小鹤双拼
    # __include: algebra_mspy           # 微软双拼
    # __include: algebra_sogou          # 搜狗双拼
    # __include: algebra_abc            # 智能ABC双拼
    # __include: algebra_ziguang        # 紫光双拼

translator:
  dictionary: melt_eng
  spelling_hints: 9

key_binder:
  import_preset: default

punctuator:
  import_preset: default

recognizer:
  import_preset: default


# 通用的派生规则
algebra_common:
  # 数字派生
  - derive/1([4-7|9])/$1teen/
  - derive/11/eleven/
  - derive/12/twelve/
  - derive/13/thirteen/
  - derive/15/fifteen/
  - derive/18/eighteen/
  - derive/0/o/ # 如 1000 -> oneOOO
  - derive/0/O/
  - derive/0/zero/
  - derive/1/one/
  - derive/10/ten/
  - derive/2/to/
  - derive/2/two/
  - derive/3/three/
  - derive/4/for/
  - derive/4/four/
  - derive/5/five/
  - derive/6/six/
  - derive/7/seven/
  - derive/8/eight/
  - derive/9/nine/
  # 符号派生
  - derive/\+/plus/
  - derive/\./dot/
  - derive/@/at/
  - derive/-/hyphen/
  - derive/#/hash/
  - derive/#/number/
  - derive/#/sharp/
  - derive/♯/sharp/
  - derive / slash
  - derive/&/and/
  - derive/%/percent/
  # 派生无单个特殊字符的拼写
  - derive/[.]//
  - derive/[+]//
  - derive/[@]//
  - derive/[-]//
  - derive/[_]//
  # 派生无任何非字母数字字符的拼写
  - derive/[^a-zA-Z0-9]//
  # 禁用非英文、数字开头的编码
  - erase/^[^a-zA-Z0-9].+$/
  # 全小写
  - derive/^.+$/\L$0/
  # 全大写
  - derive/^.+$/\U$0/
  # 首字母大写
  - derive/^./\U$0/
  # 前 2~10 个字母大写
  - derive/^([a-z]{2})/\U$1/
  - derive/^([a-z]{3})/\U$1/
  - derive/^([a-z]{4})/\U$1/
  - derive/^([a-z]{5})/\U$1/
  - derive/^([a-z]{6})/\U$1/
  - derive/^([a-z]{7})/\U$1/
  - derive/^([a-z]{8})/\U$1/
  - derive/^([a-z]{9})/\U$1/
  - derive/^([a-z]{10})/\U$1/

# 全拼
algebra_rime_ice:
  __include: algebra_common
  __append:
    - derive/(?<!\d)1([1-9])(?!\d)/shi$1/
    - derive/([1-9])0000(?!0)/$1wan/
    - derive/([1-9])000(?!0)/$1qian/
    - derive/([1-9])00(?!0)/$1bai/
    - derive/([2-9])0(?!0)/$1shi/
    - derive/(?<!\d)([2-9])([1-9])(?!\d)/$1shi$2/
    - derive/\./dian/
    - derive/10/shi/
    - derive/0/ling/
    - derive/1/yi/
    - derive/2/er/
    - derive/2/liang/
    - derive/3/san/
    - derive/4/si/
    - derive/5/wu/
    - derive/6/liu/
    - derive/7/qi/
    - derive/8/ba/
    - derive/9/jiu/
    - derive/\+/jia/
    - derive/#/jing/
# 自然码双拼
algebra_double_pinyin:
  __include: algebra_common
  __append:
    - derive/(?<!\d)1([1-9])(?!\d)/ui$1/
    - derive/([1-9])0000(?!0)/$1wj/
    - derive/([1-9])000(?!0)/$1qm/
    - derive/([1-9])00(?!0)/$1bl/
    - derive/([2-9])0(?!0)/$1ui/
    - derive/(?<!\d)([2-9])([1-9])(?!\d)/$1ui$2/
    - derive/\./dm/
    - derive/10/ui/
    - derive/0/ly/
    - derive/1/yi/
    - derive/2/er/
    - derive/2/ld/
    - derive/3/sj/
    - derive/4/si/
    - derive/5/wu/
    - derive/6/lq/
    - derive/7/qi/
    - derive/8/ba/
    - derive/9/jq/
    - derive/\+/jw/
    - derive/#/jy/
# 小鹤双拼
algebra_flypy:
  __include: algebra_common
  __append:
    - derive/(?<!\d)1([1-9])(?!\d)/ui$1/
    - derive/([1-9])0000(?!0)/$1wj/
    - derive/([1-9])000(?!0)/$1qm/
    - derive/([1-9])00(?!0)/$1bd/
    - derive/([2-9])0(?!0)/$1ui/
    - derive/(?<!\d)([2-9])([1-9])(?!\d)/$1ui$2/
    - derive/\./dm/
    - derive/10/ui/
    - derive/0/lk/
    - derive/1/yi/
    - derive/2/er/
    - derive/2/ll/
    - derive/3/sj/
    - derive/4/si/
    - derive/5/wu/
    - derive/6/lq/
    - derive/7/qi/
    - derive/8/ba/
    - derive/9/jq/
    - derive/\+/jx/
    - derive/#/jk/
# 微软双拼
algebra_mspy:
  __include: algebra_common
  __append:
    - derive/(?<!\d)1([1-9])(?!\d)/ui$1/
    - derive/([1-9])0000(?!0)/$1wj/
    - derive/([1-9])000(?!0)/$1qm/
    - derive/([1-9])00(?!0)/$1bl/
    - derive/([2-9])0(?!0)/$1ui/
    - derive/(?<!\d)([2-9])([1-9])(?!\d)/$1ui$2/
    - derive/\./dm/
    - derive/10/ui/
    - derive/0/l;/
    - derive/1/yi/
    - derive/2/er/
    - derive/2/or/
    - derive/2/ld/
    - derive/3/sj/
    - derive/4/si/
    - derive/5/wu/
    - derive/6/lq/
    - derive/7/qi/
    - derive/8/ba/
    - derive/9/jq/
    - derive/\+/jw/
    - derive/#/j;/
# 搜狗双拼
algebra_sogou:
  __include: algebra_common
  __append:
    - derive/(?<!\d)1([1-9])(?!\d)/ui$1/
    - derive/([1-9])0000(?!0)/$1wj/
    - derive/([1-9])000(?!0)/$1qm/
    - derive/([1-9])00(?!0)/$1bl/
    - derive/([2-9])0(?!0)/$1ui/
    - derive/(?<!\d)([2-9])([1-9])(?!\d)/$1ui$2/
    - derive/\./dm/
    - derive/10/ui/
    - derive/0/l;/
    - derive/1/yi/
    - derive/2/er/
    - derive/2/or/
    - derive/2/ld/
    - derive/3/sj/
    - derive/4/si/
    - derive/5/wu/
    - derive/6/lq/
    - derive/7/qi/
    - derive/8/ba/
    - derive/9/jq/
    - derive/\+/jw/
    - derive/#/jy/
# 智能ABC双拼
algebra_abc:
  __include: algebra_common
  __append:
    - derive/(?<!\d)1([1-9])(?!\d)/vi$1/
    - derive/([1-9])0000(?!0)/$1wj/
    - derive/([1-9])000(?!0)/$1qw/
    - derive/([1-9])00(?!0)/$1bl/
    - derive/([2-9])0(?!0)/$1vi/
    - derive/(?<!\d)([2-9])([1-9])(?!\d)/$1vi$2/
    - derive/\./dw/
    - derive/10/vi/
    - derive/0/ly/
    - derive/1/yi/
    - derive/2/er/
    - derive/2/or/
    - derive/2/lt/
    - derive/3/sj/
    - derive/4/si/
    - derive/5/wu/
    - derive/6/lr/
    - derive/7/qi/
    - derive/8/ba/
    - derive/9/jr/
    - derive/\+/jd/
    - derive/#/jy/
# 紫光双拼
algebra_ziguang:
  __include: algebra_common
  __append:
    - derive/(?<!\d)1([1-9])(?!\d)/ii$1/
    - derive/([1-9])0000(?!0)/$1wr/
    - derive/([1-9])000(?!0)/$1qf/
    - derive/([1-9])00(?!0)/$1bp/
    - derive/([2-9])0(?!0)/$1ii/
    - derive/(?<!\d)([2-9])([1-9])(?!\d)/$1ii$2/
    - derive/\./df/
    - derive/10/ii/
    - derive/0/l;/
    - derive/1/yi/
    - derive/2/er/
    - derive/2/oj/
    - derive/2/lg/
    - derive/3/sr/
    - derive/4/si/
    - derive/5/wu/
    - derive/6/lj/
    - derive/7/qi/
    - derive/8/ba/
    - derive/9/jj/
    - derive/\+/jx/
    - derive/#/j;/
