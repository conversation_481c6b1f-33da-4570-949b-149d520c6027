name: Pack and Release

on:
  push:
    branches:
      - main
    tags:
      - '[0-9]+.*'
    paths:
      - '**/**'
      - '!**.md'
      - '!**.gitignore'
      - '!others/**'
      - '!.github/**'
  workflow_dispatch:

jobs:
  Release:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Pack dicts
        run: |
          mkdir dist
          echo "Pack all ..."
          find . -maxdepth 1 -name "*.lua" -o -name "*.yaml" -o -name "*.txt" | zip dist/full.zip -@
          zip -r dist/full.zip cn_dicts en_dicts lua opencc LICENSE
          echo "Pack dicts ..."
          zip -r dist/all_dicts.zip cn_dicts en_dicts radical_pinyin.dict.yaml
          echo "Pack cn_dicts ..."
          zip -r dist/cn_dicts.zip cn_dicts
          echo "Pack en_dicts ..."
          zip -r dist/en_dicts.zip en_dicts
          echo "Pack opencc ..."
          zip -r dist/opencc.zip opencc

      - name: Create nightly release
        if: ${{ github.ref == 'refs/heads/main' }}
        uses: 'softprops/action-gh-release@v2'
        with:
          body: |
            ## 说明
            
            这里是每次提交后自动打包的版本，包含最新的功能和词库

            - `full.zip` : 包含所有词典和方案文件
            - `cn_dicts.zip`：中文词库
            - `en_dicts.zip`：英文词库
            - `opencc.zip`：opencc 词库（emoji 等）
            - `all_dicts.zip`：以上三个词库的整合

          tag_name: nightly
          name: "nightly build"
          make_latest: true
          files: |
            dist/*

      - name: Create stable release
        if: startsWith(github.ref, 'refs/tags/')
        uses: 'softprops/action-gh-release@v2'
        with:
          body_path: ${{ github.workspace }}/others/CHANGELOG.md
          draft: true
          make_latest: true
          files: |
            dist/*