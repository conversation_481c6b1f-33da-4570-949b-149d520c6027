-- 此插件支持按8键打开简拼模式
-- 如：输入 xyz8 将变成 x'y'z'
-- 再次按8键取消简拼模式

local TransformJianpin = {}

function TransformJianpin.init(env)
    local config = env.engine.schema.config
    -- 获取配置的触发键
    TransformJianpin.transform_jianpin_trigger_key = config:get_string("key_binder/transform_jianpin_trigger") or "8"
end

function TransformJianpin.func(key, env)
    -- 用户按键的处理
    local input_code = env.engine.context.input
    -- 如果当前按键为触发键且按下了8键，则进行简拼处理
    if key:repr() == TransformJianpin.transform_jianpin_trigger_key then
        -- 清空context上下文
        env.engine.context:clear()
        if string.find(input_code, "'") then -- 已经是简拼模式，取消时
            -- 清空输入中的 简拼标记' 下次输入时恢复正常
            env.engine.context:push_input(input_code:gsub("[^%a]", ""))
        else -- 进入简拼模式时
            -- 将当前输入转为简拼模式，每个字母后加上'
            env.engine.context:push_input(string.gsub(input_code:gsub("[^%a]", ""), "(.)", "%1'"):sub(1, -1))
        end
    end
    return 2
end

return TransformJianpin